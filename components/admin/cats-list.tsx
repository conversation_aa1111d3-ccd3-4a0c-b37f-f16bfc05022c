"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	Di<PERSON>Header,
	DialogTitle,
} from "@/components/ui/dialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Edit, Trash, Eye } from "lucide-react";
import {
	DataTable,
	type Column,
	type FilterOption,
	type Action,
} from "./data-table";
import { StatusBadge } from "./status-badge";
import { api } from "@/lib/trpc/react";

interface Cat {
	id: number;
	name: string;
	breed: string;
	age: number;
	gender: "male" | "female";
	status: "available" | "pending" | "adopted" | "unavailable";
	createdAt: Date;
	updatedAt: Date;
	slug: string;
	owner: {
		id: number;
		name: string;
		email: string;
		role: string;
	};
	images: Array<{
		id: number;
		url: string;
		order: number;
	}>;
	wilaya?: { name: string };
	commune?: { name: string };
}

interface AdminCatsListProps {
	limit?: number;
}

export function AdminCatsList({ limit }: AdminCatsListProps) {
	const t = useTranslations("admin");
	const { toast } = useToast();
	
	// State for table controls
	const [search, setSearch] = useState("");
	const [statusFilter, setStatusFilter] = useState("");
	const [sortBy, setSortBy] = useState<"name" | "createdAt" | "updatedAt">("createdAt");
	const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
	const [page, setPage] = useState(0);
	const pageSize = limit || 20;
	
	// State for dialogs
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [statusDialogOpen, setStatusDialogOpen] = useState(false);
	const [selectedCat, setSelectedCat] = useState<Cat | null>(null);
	const [newStatus, setNewStatus] = useState<"available" | "pending" | "adopted" | "unavailable">("available");
	
	// API queries and mutations
	const { data: catsData, isLoading, refetch } = api.admin.listCats.useQuery({
		limit: pageSize,
		offset: page * pageSize,
		search: search || undefined,
		status: statusFilter as any || undefined,
		sortBy,
		sortOrder,
	});
	
	const updateStatusMutation = api.admin.updateCatStatus.useMutation({
		onSuccess: () => {
			toast({
				title: t("cats.statusUpdated"),
				description: t("cats.statusUpdatedDescription"),
			});
			refetch();
			setStatusDialogOpen(false);
		},
		onError: (error) => {
			toast({
				title: t("common.error"),
				description: error.message,
				variant: "destructive",
			});
		},
	});
	
	const deleteCatMutation = api.admin.deleteCat.useMutation({
		onSuccess: () => {
			toast({
				title: t("cats.catDeleted"),
				description: t("cats.catDeletedDescription"),
			});
			refetch();
			setDeleteDialogOpen(false);
		},
		onError: (error) => {
			toast({
				title: t("common.error"),
				description: error.message,
				variant: "destructive",
			});
		},
	});

	// Table configuration
	const columns: Column<Cat>[] = [
		{
			key: "cat",
			label: t("cats.table.cat"),
			render: (cat) => (
				<div className="flex items-center space-x-3">
					<div className="relative h-10 w-10 rounded-md overflow-hidden bg-muted">
						<Image
							src={cat.images[0]?.url || "https://placehold.co/100x100/e2e8f0/94a3b8?text=Cat"}
							alt={cat.name}
							fill
							className="object-cover"
							onError={(e) => {
								e.currentTarget.src = "https://placehold.co/100x100/e2e8f0/94a3b8?text=Cat";
							}}
						/>
					</div>
					<div>
						<div className="font-medium">{cat.name}</div>
						<div className="text-sm text-muted-foreground">ID: {cat.id}</div>
					</div>
				</div>
			),
		},
		{
			key: "details",
			label: t("cats.table.details"),
			render: (cat) => (
				<div className="text-sm">
					<div>{cat.breed}</div>
					<div>
						{cat.age} {t("cats.yearsOld")} • {t(`cats.gender.${cat.gender}`)}
					</div>
					<div className="text-muted-foreground">
						{cat.wilaya?.name && cat.commune?.name
							? `${cat.commune.name}, ${cat.wilaya.name}`
							: t("common.notSpecified")}
					</div>
				</div>
			),
		},
		{
			key: "owner",
			label: t("cats.table.postedBy"),
			render: (cat) => (
				<div className="text-sm">
					<div>{cat.owner.name}</div>
					<div className="text-muted-foreground">
						{new Date(cat.createdAt).toLocaleDateString()}
					</div>
				</div>
			),
		},
		{
			key: "status",
			label: t("cats.table.status"),
			sortable: true,
			render: (cat) => <StatusBadge status={cat.status} type="cat" />,
		},
	];

	const filters: FilterOption[] = [
		{
			key: "status",
			label: t("cats.filters.status"),
			options: [
				{ value: "available", label: t("status.cat.available") },
				{ value: "pending", label: t("status.cat.pending") },
				{ value: "adopted", label: t("status.cat.adopted") },
				{ value: "unavailable", label: t("status.cat.unavailable") },
			],
		},
	];

	const actions: Action<Cat>[] = [
		{
			label: t("cats.actions.view"),
			icon: <Eye className="h-4 w-4" />,
			onClick: (cat) => {
				window.open(`/cats/${cat.slug}`, '_blank');
			},
		},
		{
			label: t("cats.actions.changeStatus"),
			icon: <Edit className="h-4 w-4" />,
			onClick: (cat) => {
				setSelectedCat(cat);
				setNewStatus(cat.status);
				setStatusDialogOpen(true);
			},
		},
		{
			label: t("cats.actions.delete"),
			icon: <Trash className="h-4 w-4" />,
			variant: "destructive",
			onClick: (cat) => {
				setSelectedCat(cat);
				setDeleteDialogOpen(true);
			},
		},
	];

	const handleStatusUpdate = () => {
		if (!selectedCat) return;
		
		updateStatusMutation.mutate({
			catId: selectedCat.id,
			status: newStatus,
		});
	};

	const handleCatDelete = () => {
		if (!selectedCat) return;
		
		deleteCatMutation.mutate({
			catId: selectedCat.id,
		});
	};

	const cats = catsData?.cats || [];

	return (
		<div className="space-y-4">
			<DataTable
				data={cats}
				columns={columns}
				loading={isLoading}
				searchPlaceholder={t("cats.searchPlaceholder")}
				searchValue={search}
				onSearchChange={setSearch}
				filters={filters}
				filterValues={{ status: statusFilter }}
				onFilterChange={(key, value) => {
					if (key === "status") setStatusFilter(value);
				}}
				sortBy={sortBy}
				sortOrder={sortOrder}
				onSortChange={(key, order) => {
					setSortBy(key as any);
					setSortOrder(order);
				}}
				actions={actions}
				pagination={
					catsData
						? {
								total: catsData.total,
								limit: pageSize,
								offset: page * pageSize,
								onPageChange: (offset) => setPage(offset / pageSize),
						  }
						: undefined
				}
				emptyMessage={t("cats.noCats")}
			/>

			{/* Status Change Dialog */}
			<Dialog open={statusDialogOpen} onOpenChange={setStatusDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>{t("cats.changeStatus")}</DialogTitle>
						<DialogDescription>
							{t("cats.changeStatusDescription", { name: selectedCat?.name })}
						</DialogDescription>
					</DialogHeader>
					<div className="py-4">
						<Select value={newStatus} onValueChange={(value: any) => setNewStatus(value)}>
							<SelectTrigger>
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="available">{t("status.cat.available")}</SelectItem>
								<SelectItem value="pending">{t("status.cat.pending")}</SelectItem>
								<SelectItem value="adopted">{t("status.cat.adopted")}</SelectItem>
								<SelectItem value="unavailable">{t("status.cat.unavailable")}</SelectItem>
							</SelectContent>
						</Select>
					</div>
					<DialogFooter>
						<Button variant="outline" onClick={() => setStatusDialogOpen(false)}>
							{t("common.cancel")}
						</Button>
						<Button 
							onClick={handleStatusUpdate}
							disabled={updateStatusMutation.isPending}
						>
							{updateStatusMutation.isPending ? t("common.updating") : t("common.update")}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Delete Confirmation Dialog */}
			<Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>{t("cats.deleteCat")}</DialogTitle>
						<DialogDescription>
							{t("cats.deleteCatDescription", { name: selectedCat?.name })}
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
							{t("common.cancel")}
						</Button>
						<Button 
							variant="destructive" 
							onClick={handleCatDelete}
							disabled={deleteCatMutation.isPending}
						>
							{deleteCatMutation.isPending ? t("common.deleting") : t("common.delete")}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}
